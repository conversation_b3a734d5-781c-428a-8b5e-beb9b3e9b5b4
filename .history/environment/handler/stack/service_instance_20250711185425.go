package stack

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	service2 "git.sirius.io/the-platform/backend/environment/models/service"

	errcode "git.platform.io/environment/environment/common/code"
	"git.platform.io/environment/environment/dao/gitlabdao"
	dao "git.platform.io/environment/environment/dao/stack"
	"git.platform.io/environment/environment/handler/configure"
	model "git.platform.io/environment/environment/model/configure"
	"git.platform.io/environment/environment/model/release"
	"git.platform.io/environment/environment/model/stack"
	"git.platform.io/resource/common"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/common/utils"
	"git.platform.io/resource/information"
	"github.com/go-xorm/xorm"
	"github.com/xanzy/go-gitlab"
)

// 服务实例
type ServiceInstance struct {
	configure.Entity
	StackID         uint64
	PackageID       uint64
	ServiceId       string
	BaseVersionID   uint64
	FieldScope      information.NumberRange
	FieldSet        *configure.FieldSet
	SectionCodeList []string
}

func VirtualServiceInstance(planInfo *release.Plan, serviceType, serviceId string) *ServiceInstance {
	return &ServiceInstance{
		StackID:       planInfo.StackID,
		ServiceId:     serviceId,
		PackageID:     planInfo.PackageID,
		BaseVersionID: planInfo.VersionID,
		Entity: configure.Entity{
			Scope: model.PatchScope{
				PlanID:        planInfo.ID,
				ServiceCode:   serviceId,
				ServiceType:   serviceType,
				SuType:        planInfo.SuTypeCode,
				EnvironmentID: planInfo.EnvironmentID,
				SuInstanceID:  planInfo.SuInstanceID,
				SuID:          planInfo.SuID,
			},
		},
		Sections: make([]configure.ISection, 0),
	}
}

func NewServiceInstance(planID, packageId, instanceID uint64, ctx context.Context) *ServiceInstance {
	info, err := GetServiceInstanceInfo(nil, instanceID, ctx)
	if err != nil {
		log.Errorf(ctx, "GetServiceInstanceInfo [%d] fail,as[%s]", instanceID, err.Error())
		return nil
	}
	return &ServiceInstance{
		StackID:         info.StackID,
		ServiceId:       info.ServiceId,
		PackageID:       packageId,
		BaseVersionID:   info.CurrentVersionID,
		SectionCodeList: info.Sections,
		Entity: configure.Entity{
			Scope: model.PatchScope{
				PlanID:        planID,
				InstanceID:    instanceID,
				ServiceCode:   info.Code,
				ServiceType:   info.ServiceType,
				SuType:        info.SuTypeCode,
				EnvironmentID: info.EnvironmentID,
				SuInstanceID:  info.SuInstanceID,
				SuID:          info.SuID,
			},
			Ctx: ctx,
		},
	}
}

func (s *ServiceInstance) AutoSetSections(domains ...string) *app.Fault {
	var filter model.SectionFilter
	if len(s.SectionCodeList) > 0 {
		filter = model.SectionFilter{
			StackID:  s.StackID,
			CodeList: s.SectionCodeList,
		}
	} else {
		domainList := []string{model.SectionDomainAll, model.SectionDomainService}
		if len(domains) > 0 {
			domainList = append(domainList, domains...)
		} else {
			domainList = append(domainList, s.Scope.ServiceType)
		}

		filter = model.SectionFilter{
			StackID:     s.StackID,
			DomainList:  domainList,
			ServiceType: s.Scope.ServiceType,
		}
	}

	return s.SetSectionWithFilter(&filter)
}

func (s *ServiceInstance) SetSections(domains ...string) *app.Fault {
	domainList := []string{model.SectionDomainAll, model.SectionDomainService}
	for _, domain := range domains {
		if !common.StringInList(domain, domainList) {
			domainList = append(domainList, domain)
		}
	}

	filter := model.SectionFilter{
		StackID:     s.StackID,
		DomainList:  domainList,
		ServiceType: s.Scope.ServiceType,
	}
	return s.SetSectionWithFilter(&filter)
}

func (s *ServiceInstance) SetSectionsByFilter(filter *model.SectionFilter) *app.Fault {
	filter.Ctx = s.Ctx
	sections, err := configure.GetSectionList(filter)
	if err != nil {
		return app.NewFaultf(errcode.QueryRecordFail, "GetServiceSectionList fail,as[%s]", err.Error())
	}

	arr := make([]configure.ISection, 0)
	for i := 0; i < len(sections); i++ {
		entity := NewInstanceSection(&sections[i], s.Scope, s.Ctx)
		entity.FieldSet = s.FieldSet
		entity.FieldScope = s.FieldScope
		fault := entity.LoadFields()
		if fault != nil {
			return fault
		}
		arr = append(arr, entity)
	}

	s.Sections = arr
	return nil
}

func (s *ServiceInstance) SetSectionWithDomain(domains ...string) *app.Fault {
	// 加载全局默认配置
	fault := s.SetSections(domains...)
	if fault != nil {
		log.Errorf(s.Ctx, "SetSections for service [%s] fail,as[%s]", s.ServiceId, fault.Error())
		return fault
	}

	fault = s.MergeFields()
	if fault != nil {
		log.Errorf(s.Ctx, "MergeFields for service [%s] fail,as[%s]", s.ServiceId, fault.Error())
		return fault
	}

	return nil
}

func (s *ServiceInstance) SetSectionWithFilter(filter *model.SectionFilter) *app.Fault {
	// 加载全局默认配置
	fault := s.SetSectionsByFilter(filter)
	if fault != nil {
		log.Errorf(s.Ctx, "SetSections for service [%s] fail,as[%s]", s.ServiceId, fault.Error())
		return fault
	}

	fault = s.MergeFields()
	if fault != nil {
		log.Errorf(s.Ctx, "MergeFields for service [%s] fail,as[%s]", s.ServiceId, fault.Error())
		return fault
	}

	return nil
}

func (s *ServiceInstance) Restore(cfg utils.Configure) *app.Fault {
	if s.BaseVersionID == 0 {
		s.SetValue(cfg, 0)
	} else {
		version, err := GetServiceInstanceVersion(s.BaseVersionID, s.Ctx)
		if err != nil {
			return app.NewFaultf(errcode.QueryRecordFail, "GetServiceInstanceVersion[%d]fail,as[%s]", s.BaseVersionID, err.Error())
		}
		s.SetValue(version.Data, version.LastPatchID)
	}
	return nil
}

func (s *ServiceInstance) RestoreToLatest(baseConfig utils.Configure, domains ...string) *app.Fault {
	fault := s.SetSectionWithDomain(domains...)
	if fault != nil {
		log.Errorf(s.Ctx, "init service instance [%s] fail,as[%s]", s.ServiceId, fault.Error())
		return fault
	}

	return s.Restore(baseConfig)
}

func (s *ServiceInstance) AutoRestore(baseConfig utils.Configure, domains ...string) *app.Fault {
	fault := s.AutoSetSections(domains...)
	if fault != nil {
		log.Errorf(s.Ctx, "init service instance [%s] fail,as[%s]", s.ServiceId, fault.Error())
		return fault
	}

	return s.Restore(baseConfig)
}

func (s *ServiceInstance) RestoreToLatestWithFilter(baseConfig utils.Configure, filter *model.SectionFilter) *app.Fault {
	filter.StackID = s.StackID
	fault := s.SetSectionWithFilter(filter)
	if fault != nil {
		log.Errorf(s.Ctx, "init service instance [%s] fail,as[%s]", s.ServiceId, fault.Error())
		return fault
	}

	return s.Restore(baseConfig)
}

//func (s *ServiceInstance) RestoreVersion(version *stack.ServiceInstanceVersion, domains ...string) *app.Fault {
//	s.BaseVersionID = version.ID
//	s.StackID = version.StackID
//	s.Scope.EnvironmentID = version.EnvironmentID
//	fault := s.SetSectionWithDomain(domains...)
//	if fault != nil {
//		log.Errorf("ServiceInstance [%d] init fail,as[%s]", s.Scope.InstanceID, fault.Error())
//		return fault
//	}
//
//	s.SetValue(version.Data, version.LastPatchID)
//
//	return nil
//}

func (s *ServiceInstance) CreatePatches(op *app.Operator, info *configure.PatchInfo) (uint64, *app.Fault) {
	var total uint64
	for _, section := range s.Sections {
		patchID, fault := section.CreatePatch(op, info)
		if fault != nil {
			return total, fault
		}
		if patchID > 0 {
			total += 1
		}
	}

	if total == 0 {
		log.Errorf(s.Ctx, "Create 0 patch")
	}
	return total, nil
}

func (s *ServiceInstance) CreatePatch(op *app.Operator, info *configure.PatchInfo) (uint64, *app.Fault) {
	if info.Note == "" {
		info.Note = fmt.Sprintf("user [%s] set config", op.Name)
	}

	return s.CreatePatches(op, info)
}

func (s *ServiceInstance) GetLastPatchID() uint64 {
	var lastPatchID uint64
	for _, section := range s.Sections {
		patchID := section.GetLastPatchID()
		if patchID > lastPatchID {
			lastPatchID = patchID
		}
	}
	return lastPatchID
}

func (s *ServiceInstance) GetFirstPatchID() uint64 {
	var firstPatchID uint64
	for _, section := range s.Sections {
		patchID := section.GetFirstPatchID()
		if firstPatchID == 0 || (patchID > 0 && patchID < firstPatchID) {
			firstPatchID = patchID
		}
	}
	return firstPatchID
}

func (s *ServiceInstance) AcceptPatches(patches []model.Patch) (int, *app.Fault) {
	patchList := configure.ConvertToIPatchList(patches)
	total := 0
	for _, section := range s.Sections {
		num, fault := section.ProcessPatches(patchList)
		if fault != nil {
			return total, fault
		}
		total += num
	}

	if total == 0 {
		log.Errorf(s.Ctx, "AcceptPatches 0 patch")
	}

	return total, nil
}

func (s *ServiceInstance) SaveNewVersionData(request app.IRequest, op *app.Operator, oldConfig utils.Configure, version, note string) (uint64, *app.Fault) {
	revises, fault := s.Compare(op, oldConfig)
	if fault != nil {
		return 0, fault
	}

	last := s.GetLastPatchID()
	newVersionID, fault2 := s.SaveVersion(request, op, last, revises, version, note)
	if fault2 != nil {
		log.Errorf(s.Ctx, "SaveVersion fail,as[%s]", fault2.Error())
		return 0, fault2
	}

	return newVersionID, nil
}

func (s *ServiceInstance) SaveRevises(session *xorm.Session, records []*stack.ServiceInstanceRevise) *app.Fault {
	daoInst, err := dao.NewServiceInstanceReviseDao(session, s.Ctx)
	if log.IfError(err, "NewReviseDao") {
		return app.NewFaultf(errcode.DataBaseError, "NewReviseDao fail,as[%s]", err.Error())
	}

	// begin transaction，avoid inserting part failed
	if err := daoInst.TransactionBegin(); err != nil {
		log.Error(s.Ctx, "SaveRevises TransactionBegin fail,as:", err.Error())
		return app.NewFaultf(errcode.DataBaseError, err.Error())
	}

	// Insert data in bulk
	_, err = daoInst.Create(records)
	if err != nil {
		log.Error(s.Ctx, "SaveRevises Create fail,as:", err.Error())
		if session == nil {
			daoInst.TransactionRollback()
		}
		return app.NewFaultf(errcode.DataBaseError, err.Error())
	} else {
		if session == nil {
			_ = daoInst.TransactionCommit()
		}
		return nil
	}
}

// 通过比对当前值与新值，生成字段变更的记录
func (s *ServiceInstance) Compare(op *app.Operator, cfg utils.Configure) ([]*stack.ServiceInstanceRevise, *app.Fault) {
	arr := make([]*stack.ServiceInstanceRevise, 0)
	for _, field := range s.Fields {
		if field.IsSame(cfg) {
			continue
		}

		key := field.GetKey()
		operate := model.ReviseTypeModify
		oldValue, exist := cfg.GetString(key)
		if !exist {
			operate = model.ReviseTypeSet
		}

		revise := &stack.ServiceInstanceRevise{
			Operate:  operate,
			OldValue: oldValue,
			NewValue: field.GetStringValue(),
		}
		revise.CreatorID = op.UserID
		revise.Creator = op.Name
		revise.TenantID = op.TenantID
		revise.FieldID = field.ID
		revise.ServiceId = s.ServiceId
		arr = append(arr, revise)
	}

	return arr, nil
}

func (s *ServiceInstance) NewRevise(key string, cfg utils.Configure) (*stack.ServiceInstanceRevise, *app.Fault) {
	if newValue, exist := cfg.GetString(key); exist {
		if oldValue, ok := s.Configure.GetString(key); ok {
			if oldValue == newValue {
				return nil, nil
			} else {
				return &stack.ServiceInstanceRevise{
					Operate:  model.ReviseTypeModify,
					OldValue: oldValue,
					NewValue: newValue,
				}, nil
			}
		} else {
			return &stack.ServiceInstanceRevise{
				Operate:  model.ReviseTypeSet,
				OldValue: oldValue,
				NewValue: newValue,
			}, nil
		}
	} else {
		log.Errorf(s.Ctx, "SectionBase Compare(service_instance) found a property[%s] void value", key)
		return nil, app.NewFaultf(errcode.InternalError, "property[%s] void value", key)
	}
}

// 保存一个版本
func (s *ServiceInstance) SaveVersion(request app.IRequest, op *app.Operator, lastPatchID uint64, revises []*stack.ServiceInstanceRevise, version, note string) (uint64, *app.Fault) {
	instVersion := stack.ServiceInstanceVersion{
		StackID:       s.StackID,
		InstanceID:    s.Scope.InstanceID,
		EnvironmentID: s.Scope.EnvironmentID,
		// SuInstanceID:  s.Scope.SuInstanceID,
		// SuID:          s.Scope.SuID,
		LastPatchID:   lastPatchID,
		BaseVersionID: s.BaseVersionID,
		Status:        "",
		Journal:       "",
		Data:          s.Configure,
		Version:       version,
		Description:   note,
		TenantID:      op.TenantID,
		Creator:       op.Name,
		CreatorID:     op.UserID,
	}

	mgr := NewServiceInstanceVersionMgr(request.GetSession(), &instVersion, s.Ctx)
	err := mgr.Save()
	if err != nil {
		return 0, app.NewFaultf(errcode.InsertRecordFail, "save service instance version fail,as[%s]", err.Error())
	}
	// set version into IRequest
	// request.SetContent(ResourceKeySviVersion, fmt.Sprintf("%d-%s", s.PackageID, s.ServiceId), instVersion)

	for i := 0; i < len(revises); i++ {
		revises[i].ServiceVersionID = instVersion.ID
	}

	fault := s.SaveRevises(request.GetSession(), revises)
	if fault != nil {
		log.Errorf(s.Ctx, "SaveRevises fail,as[%s]", fault.Error())
		return 0, fault
	}

	return instVersion.ID, nil
}

func (s *ServiceInstance) SaveToGit(request app.IRequest) *app.Fault {
	// write service instance patch config to gitlab
	scfg, _ := json.Marshal(s.Configure)
	svcinsdao, _ := dao.NewServiceInstanceDao(nil, s.Ctx)
	svci, _ := svcinsdao.ReadServiceInstance(s.Scope.InstanceID)
	file := svci.Name + ".json"
	eid := s.Scope.EnvironmentID
	_, err := WriteConfigToGdb(request, gitlabdao.GitlabClient, eid, file, "master", svci, scfg)
	if err != nil {
		return app.NewFaultf(errcode.QueryRecordFail, "get env id fail,as[%s]", err)
	}
	log.Infof(s.Ctx, "Write service instance config to [%s] successed.\n", file)
	return nil
}

func (s *ServiceInstance) SaveToGitWithSession(request app.IRequest) *app.Fault {
	// write service instance patch config to gitlab
	scfg, _ := json.Marshal(s.Configure)
	svcinsdao, _ := dao.NewServiceInstanceDao(request.GetSession(), s.Ctx)
	svci, _ := svcinsdao.ReadServiceInstance(s.Scope.InstanceID)
	file := svci.Name + ".json"
	eid := s.Scope.EnvironmentID
	_, err := WriteConfigToGdb(request, gitlabdao.GitlabClient, eid, file, "master", svci, scfg)
	if err != nil {
		return app.NewFaultf(errcode.QueryRecordFail, "get env id fail,as[%s]", err)
	}
	log.Infof(s.Ctx, "Write service instance config to [%s] successed.\n", file)
	return nil
}

func WriteConfigToGdb(request app.IRequest, client *gitlab.Client, eid uint64, file, branch string, svc *stack.ServiceInstance, scfg []byte) (string, *app.Fault) {
	// var projectName string
	// env, err := GetEnvironmentWithSession(request, eid)
	// if err != nil {
	// 	return "", app.NewFaultf(errcode.QueryRecordFail, "get env info [%d] fail,as[%s]", eid, err.Error())
	// }

	// //Get service project name to the service type
	// var repoId = env.RepoID
	// _, ok := stack.ServiceCommonTypeMaps[svc.ServiceType]
	// if ok {
	// 	projectName = constants.CTX_KEY_ENVIRONMENT_SERVICE_PROJECTNAME
	// } else {
	// 	switch svc.ServiceType {
	// 	case "AIG", "aig", "INGRESS", "ingress":
	// 		projectName = constants.CTX_KEY_ENVIRONMENT_INGRESSDEFAULT_PROJECTNAME
	// 	case "AOG", "aog", "OUTGRESS", "outgress":
	// 		projectName = constants.CTX_KEY_ENVIRONMENT_OUTGRESSDEFAULT_PROJECTNAME
	// 		projectName = fmt.Sprintf("%s_%s", svc.ServiceId, svc.Version)
	// 	default:
	// 		if svc.PackageID > 0 { // mu components
	// 			id, f := gitlabbase.GetProjectInGroupIDWithCreate(svc.ServiceType, repoId, 1, 1, true) // check component folder
	// 			if id == 0 || f != nil {
	// 				return "", app.NewFaultf(errcode.QueryRecordFail, "get service project id fail,as[%s]", f.ErrorMsg)
	// 			}
	// 			repoId = id
	// 			projectName = fmt.Sprintf("%s_%s", svc.ServiceId, svc.Version)
	// 		} else {
	// 			return "", app.NewFaultf(errcode.QueryRecordFail, "can not found service project by service type,as[%s]", svc.ServiceType)
	// 		}
	// 	}
	// }

	// Get service project id from git.
	// id, f := gitlabbase.GetProjectInGroupIDWithCreate(projectName, repoId, 1, 100, false)
	// if id == 0 || f != nil {
	// 	return "", app.NewFaultf(errcode.QueryRecordFail, "get service project id fail,as[%s]", f.ErrorMsg)
	// }

	// Get service project file,if not exist will be created.
	// e := gitlabbase.WirteAndUpdateGfile(client, id, file, scfg)
	// if e != nil {
	// 	return "", app.NewFaultf(errcode.QueryRecordFail, "write service project file fail,as[%s]", e.Message)
	// }

	// get file commit id
	// client = gitlabdao.GitlabCUDClient
	// fileMeta, _, f := gitlabdao.GetFileMetaData(client, id, file, "master")
	// if f != nil {
	// 	return "", app.NewFaultf(errcode.GroupDeleteFail, "get service config commit id fail,as[%s]", f.ErrorMsg)
	// }

	// return fileMeta.CommitID, nil
	return "", nil
}

type PlanParam struct {
	PlanID            uint64                 `json:"planId"`
	PackageID         uint64                 `json:"packageId"`
	Name              string                 `json:"name"`
	ServiceInstanceID uint64                 `json:"serviceInstanceId"`
	CurrentVersionID  uint64                 `json:"currentVersionId"`
	Version           string                 `json:"version"`
	Note              string                 `json:"note"`
	Patches           []model.Patch          `json:"patches"`
	BaseConfigure     map[string]interface{} `json:"baseConfigure"`
	Configure         map[string]interface{} `json:"configure"`
}

func UpdateServiceInstanceConfig(request app.IRequest, op *app.Operator, param PlanParam, sectionDomain ...string) (uint64, *app.Fault) {
	entity := NewServiceInstance(param.PlanID, param.PackageID, param.ServiceInstanceID, request.GetCtx())
	fault := entity.AutoRestore(param.BaseConfigure, sectionDomain...)
	if fault != nil {
		log.Errorf(request.GetCtx(), "Restore for service instance [%s] fail,as[%s]", param.Name, fault.Error())
		return 0, fault
	}

	oldConfig := entity.Configure.Clone()

	total, fault := entity.AcceptPatches(param.Patches)
	if fault != nil {
		log.Errorf(request.GetCtx(), "patch to service instance version [%d] fail,as[%s]", param.CurrentVersionID, fault.Error())
		return 0, fault
	}

	// 没有变更，返回旧版本
	if total == 0 && entity.BaseVersionID > 0 && len(param.Configure) == 0 {
		return entity.BaseVersionID, nil
	}

	entity.Configure = entity.Snapshot()
	if len(param.Configure) > 0 {
		entity.Configure.Apply(param.Configure)
		txt, _ := json.Marshal(entity.Configure)
		log.Infof(request.GetCtx(), "entity.cfg=[%s]", txt)
	}
	// 兼容旧版本
	if param.Version != "" {
		entity.Configure[service2.ConfigKeyGitlabVersion] = param.Version
		log.Infof(request.GetCtx(), "entity.cfg=[%v]", entity.Configure)
	}

	versionId, fault2 := entity.SaveNewVersionData(request, op, oldConfig, param.Version, param.Note)
	if fault2 != nil {
		log.Errorf(request.GetCtx(), "patch to service instance version [%d] fail,as[%s]", param.CurrentVersionID, fault2.Error())
		return 0, fault2
	}

	return versionId, nil
}

func NewReleaseName(serviceCode string, index int) string {
	code := fmt.Sprintf("%X", index)
	if len(code) == 1 {
		code = "0" + code
	}
	return strings.ToLower(fmt.Sprintf("%s-%s", serviceCode, code))
}

func GetServiceInstanceID(request app.IRequest, op *app.Operator, plan *release.Plan, instanceRef release.ServiceInstanceRef,
	rdbClusterId string, dbs []string, brokerVpnID, stackInstanceID uint64, releaseCode string,
) (*stack.ServiceInstance, *app.Fault) {
	filter := stack.ServiceInstanceFilter{
		StackInstanceID: stackInstanceID,
		BrokerVpnID:     brokerVpnID,
		ServiceRefID:    instanceRef.ID,
		ServiceID:       instanceRef.ServiceId,
		SuInstanceID:    plan.SuInstanceID,
	}
	filter.Ctx = request.GetCtx()
	instances, err := GetServiceInstanceByFilter(nil, &filter)
	if err != nil {
		return nil, app.NewFaultf(errcode.QueryRecordFail, "GetServiceInstanceByFilter fail,as[%s]", err.Error())
	}

	if len(instances) > 0 {
		return &instances[0], nil
	}

	dbUser := ""
	dbPwd := ""
	if len(dbs) > 0 {
		dbUser = fmt.Sprintf("%s-%s-%x-%x", instanceRef.ServiceType, instanceRef.ServiceId, instanceRef.PlanID, instanceRef.ID)
		dbPwd = fmt.Sprintf("%s-%s_%x@1%X", strings.ToUpper(instanceRef.ServiceType), strings.ToLower(instanceRef.ServiceId), instanceRef.PlanID, instanceRef.ID)
	}
	inst := stack.ServiceInstance{
		StackID:          plan.StackID,
		ServiceRefID:     instanceRef.ID,
		StackInstanceID:  stackInstanceID,
		ServiceId:        instanceRef.ServiceId,
		PackageID:        instanceRef.PackageID,
		AppID:            instanceRef.AppID,
		SuTypeID:         plan.SuTypeID,
		SuTypeCode:       plan.SuTypeCode,
		Name:             instanceRef.Name,
		Code:             instanceRef.Code,
		DBClusterID:      rdbClusterId,
		DBUsername:       dbUser, // 如果这个服务属于某个宏服务， 此处要用宏服务Code
		DBPassword:       dbPwd,
		Database:         dbs,
		Sections:         instanceRef.Sections,
		ServiceType:      instanceRef.ServiceType,
		Version:          instanceRef.ServiceVersion,
		Replicas:         0,
		Published:        false,
		Status:           stack.ServiceInstanceStatusPrepare,
		RunningStatus:    "",
		BrokerVpnID:      brokerVpnID,
		ReleaseID:        0,
		ReleaseCode:      releaseCode,
		EnvironmentID:    plan.EnvironmentID,
		SuID:             plan.SuID,
		SuInstanceID:     plan.SuInstanceID,
		ServiceUuid:      instanceRef.ServiceUuid,
		CallChain:        instanceRef.CallChain,
		CurrentVersionID: 0,
		TargetVersionID:  0,
		Description:      instanceRef.Description,
		MeshRole:         instanceRef.MeshRole,
		SvrType:          instanceRef.SvrType,
		TenantID:         op.TenantID,
		UpdaterID:        op.UserID,
		Updater:          op.Name,
		CreatorID:        op.UserID,
		Creator:          op.Name,
	}

	mgr := NewServiceInstanceMgr(request.GetSession(), &inst, request.GetCtx())
	err = mgr.Save()
	if err != nil {
		return nil, app.NewFaultf(errcode.QueryRecordFail, "save instanceRef instance fail,as[%s]", err.Error())
	}

	return &inst, nil
}

func GetInstancePatcher(op *app.Operator, param *QueryFieldRequest, fieldScope information.NumberRange, patcherType string, ctx context.Context) (configure.IPatcher, *app.Fault) {
	var patcher *ServiceInstance
	var fault *app.Fault

	plan := release.Plan{
		TenantID:      op.TenantID,
		Type:          release.PlanTypePublish,
		SuTypeCode:    param.SuType,
		EnvironmentID: param.EnvironmentID,
		SuID:          param.SuID,
		SuInstanceID:  param.SuInstanceID,
	}

	if param.InstanceID <= 0 {
		plan.Type = release.PlanTypePublish
		patcher, fault = GetInstancePatcherForService(op, param, &plan, fieldScope, patcherType, ctx)
	} else {
		plan.Type = release.PlanTypeUpgrade
		patcher, fault = GetInstancePatcherForInstance(param, fieldScope, patcherType, ctx)
	}

	filter := model.PatchFilter{
		PlanID:        0,
		EnvironmentID: param.EnvironmentID,
		SuID:          param.SuID,
		SuInstanceID:  param.SuInstanceID,
		ServiceType:   param.ServiceType,
		ServiceCode:   param.ServiceCode,
		InstanceID:    param.InstanceID,
		LastPatchID:   patcher.LastPatchID,
		Status:        model.PatchStatusAccept,
	}
	filter.Ctx = ctx
 	patches, err2 := configure.GetPatchList(&filter)
	if err2 != nil {
		return nil, app.NewFaultf(errcode.QueryRecordFail, "GetInstancePatcher GetPatchByFilter fail,as[%s]",
			err2.Error())
	}

	// 服务实例接收runtime config
	if param.InstanceID > 0 {
		runtimeCfg, fault := GetRuntimeConfigForPlan(&plan, param.ServiceType, param.ServiceCode)
		if fault != nil {
			log.Errorf(ctx, "GetInstancePatcher GetRuntimeConfigForPlan fail,as[%s]", fault.Error())
			return nil, fault
		}

		patcher.SetValue(runtimeCfg, 0)
	}

	// 接收patch
	_, fault = patcher.AcceptPatches(patches)
	if fault != nil {
		log.Errorf(ctx, "GetInstancePatcher AcceptPatches fail,as[%s]", fault.Error())
		return nil, fault
	}

	return patcher, nil
}

func GetInstancePatcherForService(op *app.Operator, param *QueryFieldRequest, plan *release.Plan,
	fieldScope information.NumberRange, patcherType string, ctx context.Context,
) (*ServiceInstance, *app.Fault) {
	envConfig, err := GetEnvConfigByEnvID(op, param.EnvironmentID, ctx)
	if err != nil {
		return nil, app.NewFaultf(errcode.QueryRecordFail, "GetEnvironmentConfig [%d] fail,as[%s]",
			param.EnvironmentID, err.Error())
	}

	plan.StackID = envConfig.StackID

	// create patch
	entity := VirtualServiceInstance(plan, param.ServiceType, param.ServiceCode)
	entity.FieldScope = fieldScope
	entity.FieldSet = configure.GetPatchFieldSet(patcherType)
	fault := entity.RestoreToLatest(envConfig.Data, param.ServiceType)
	if fault != nil {
		log.Errorf(ctx, "Restore for service[%s] fail,as[%s]", param.ServiceCode, fault.Error())
		return nil, fault
	}

	return entity, nil
}

func GetInstancePatcherForInstance(param *QueryFieldRequest, fieldScope information.NumberRange, patcherType string, ctx context.Context) (*ServiceInstance, *app.Fault) {
	// create patch
	entity := NewServiceInstance(0, 0, param.InstanceID, ctx)
	entity.FieldScope = fieldScope
	entity.FieldSet = configure.GetPatchFieldSet(patcherType)
	fault := entity.RestoreToLatest(nil, param.ServiceType)
	if fault != nil {
		log.Errorf(ctx, "Restore for service[%s] fail,as[%s]", param.ServiceCode, fault.Error())
		return nil, fault
	}

	return entity, nil
}

func GetSectionPatcher(param *QueryFieldRequest, ctx context.Context) (configure.IPatcher, *app.Fault) {
	sectionInfo, err := configure.GetSection(param.SectionID, ctx)
	if err != nil {
		return nil, app.NewFaultf(errcode.QueryRecordFail, "GetSectionInfo fail,as[%s]", err.Error())
	}

	section := NewMixSection(sectionInfo, param.PatchScope, ctx)
	fault := section.Refresh()
	if fault != nil {
		log.Errorf(ctx, "refresh section fail,as[%s]", fault.Error())
		return nil, fault
	}

	return section, nil
}

package process

import (
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	errcode "git.platform.io/environment/environment/common/code"
	"git.platform.io/environment/environment/constants"
	gls3 "git.platform.io/environment/environment/dao/gls"
	dao "git.platform.io/environment/environment/dao/stack"
	"git.platform.io/environment/environment/database"
	"git.platform.io/environment/environment/handler/client/workspace"
	"git.platform.io/environment/environment/handler/gls"
	release2 "git.platform.io/environment/environment/handler/release"
	"git.platform.io/environment/environment/handler/schedule"
	"git.platform.io/environment/environment/handler/stack"
	gls2 "git.platform.io/environment/environment/model/gls"
	"git.platform.io/environment/environment/model/release"
	stack2 "git.platform.io/environment/environment/model/stack"
	"git.platform.io/environment/environment/utils"
	"git.platform.io/resource/common/app"
	"git.platform.io/resource/common/log"
	"git.platform.io/resource/information"
	"github.com/mitchellh/mapstructure"
)

func ImportSuConfigure(request app.IRequest, op *app.Operator, planInfo *release.PlanInfo, argsMaps *map[string]interface{}) *app.Result {
	var fault *app.Fault
	mgr := release2.NewPlanMgr(&planInfo.Plan, request.GetCtx())
	var err error
	if mgr.Entity.Status == release.PlanStatusPreparing {
		mgr.Entity.Status = release.PlanStatusExecuting
		err = mgr.Update("plan_status")
		if err != nil {
			fault = app.NewFaultf(errcode.UpdateRecordFail, "update env configure plan fail,as[%s]", err.Error())
			return fault.Result()
		}
	}
	defer func() {
		if fault != nil {
			mgr.Entity.Status = release.PlanStatusFailed
			mgr.Entity.Description = fault.Error()
			err = mgr.Update("plan_status", "description")
			if err != nil {
				log.Errorf(request.GetCtx(), "update plan fail,as[%s]", err.Error())
			}
		}
	}()
	args := stack.SuArgs{}
	err = mapstructure.WeakDecode((*argsMaps)[schedule.JobParamKeyArgs], &args)
	if err != nil {
		fault = app.NewFaultf(errcode.InvalidParameter, "Deploy Service Not Found, as[%s]", err.Error())
		return fault.Result()
	}
	su := args.Result

	stMap := make(map[string]workspace.RelSuTypeService, 0)
	suMap := make(map[string]stack2.Su, 0)
	siMap := make(map[string]stack2.SuInstance, 0)
	for _, st := range args.SuTypes {
		stMap[fmt.Sprint(st.ArtifactBusinessId)] = st
	}
	sus, err := stack.GetSuList(request, &stack2.SuFilter{
		TenantID:      op.TenantID,
		EnvironmentID: planInfo.EnvironmentID,
	})
	if err != nil {
		fault = app.NewFaultf(errcode.QueryRecordFail, "Get Su List fail, as[%s]", err.Error())
		return fault.Result()
	}
	for _, su := range sus {
		suMap[fmt.Sprintf("%s.%s", su.SuTypeCode, su.Code)] = su
	}
	sis, err := stack.GetSuInstanceList(request, &stack2.SuInstanceFilter{
		TenantID:      op.TenantID,
		EnvironmentID: planInfo.EnvironmentID,
	})
	if err != nil {
		fault = app.NewFaultf(errcode.QueryRecordFail, "Get Su Instance List fail, as[%s]", err.Error())
		return fault.Result()
	}
	for _, si := range sis {
		siMap[fmt.Sprintf("%d.%s", si.SuID, si.Code)] = si
	}
	suDao, err := dao.NewSuDao(request.GetSession(), request.GetCtx())
	if err != nil {
		fault = app.NewFaultf(errcode.InitDaoFail, "Get Su Dao fail, as[%s]", err.Error())
		return fault.Result()
	}
	siDao, err := dao.NewSuInstanceDao(request.GetSession(), request.GetCtx())
	if err != nil {
		fault = app.NewFaultf(errcode.InitDaoFail, "Get Su Instance Dao fail, as[%s]", err.Error())
		return fault.Result()
	}
	now := time.Now()
	switch su.Result {
	case constants.ComparisonResultAdd, constants.ComparisonResultUpd:
		st, ok := stMap[su.SuTypeId]
		if !ok {
			fault = app.NewFaultf(errcode.QueryRecordFail, "Su Type[%s] Not Found", su.SuTypeId)
			return fault.Result()
		}
		s, ok := suMap[fmt.Sprintf("%s.%s", su.SuTypeId, su.SuId)]
		if ok {
			s.Name = utils.ValidValue(su.CurrentName)
			s.Weight = int(su.CurrentWeight)
			s.ShareQuota = int(su.CurrentShardingQuota)
			_, err = suDao.UpdateSu(&s, "su_name", "weight", "share_quota")
		} else {
			s = stack2.Su{
				StackID:       planInfo.StackID,
				EnvironmentID: planInfo.EnvironmentID,
				SuTypeID:      uint64(st.Id),
				SuTypeCode:    st.ArtifactBusinessId,
				Name:          utils.ValidValue(su.CurrentName),
				Code:          utils.ValidValue(su.SuId),
				Weight:        int(su.CurrentWeight),
				ShareQuota:    int(su.CurrentShardingQuota),
				TenantID:      op.TenantID,
				Creator:       op.Name,
				CreatorID:     op.UserID,
				Updater:       op.Name,
				UpdaterID:     op.UserID,
				CreatedAt:     now,
				UpdatedAt:     now,
			}
			_, err = suDao.CreateSu(&s)
		}
		if err != nil {
			fault = app.NewFaultf(errcode.UpdateRecordFail, "Operate Su fail, as[%s]", err.Error())
			return fault.Result()
		}
		var in stack2.SuInstance
		for _, si := range su.SuInstanceList {
			switch si.Result {
			case constants.ComparisonResultAdd, constants.ComparisonResultUpd:
				in, ok = siMap[fmt.Sprintf("%d.%s", s.ID, si.SuInstanceId)]
				if ok {
					in.Name = si.CurrentSuInstanceName
					_, err = siDao.UpdateSuInstance(&in, "su_instance_name")
				} else {
					in = stack2.SuInstance{
						EnvID:     planInfo.EnvironmentID,
						SuID:      s.ID,
						Name:      utils.ValidValue(si.CurrentSuInstanceName),
						Code:      utils.ValidValue(si.SuInstanceId),
						TenantID:  op.TenantID,
						CreatorID: op.UserID,
						Creator:   op.Name,
						CreatedAt: now,
						UpdatedAt: now,
					}
					_, err = siDao.CreateSuInstance(&in)
				}
				if err != nil {
					fault = app.NewFaultf(errcode.UpdateRecordFail, "Operate Su Instance fail, as[%s]", err.Error())
					return fault.Result()
				}
			case constants.ComparisonResultDel:
				in, ok := siMap[fmt.Sprintf("%d.%s", s.ID, si.SuInstanceId)]
				if ok {
					_, err = siDao.DeleteById(in.ID)
				}
				if err != nil {
					fault = app.NewFaultf(errcode.DeleteRecordFail, "Delete Su Instance fail, as[%s]", err.Error())
					return fault.Result()
				}
			}
		}
	case constants.ComparisonResultDel:
		s, ok := suMap[fmt.Sprintf("%s.%s", su.SuTypeId, su.SuId)]
		if ok {
			for _, si := range su.SuInstanceList {
				switch si.Result {
				case constants.ComparisonResultDel:
					in, ok := siMap[fmt.Sprintf("%d.%s", s.ID, si.SuInstanceId)]
					if ok {
						_, err = siDao.DeleteById(in.ID)
					}
					if err != nil {
						fault = app.NewFaultf(errcode.DeleteRecordFail, "Delete Su Instance fail, as[%s]", err.Error())
						return fault.Result()
					}
				}
			}
			_, err = suDao.DeleteSu(s.ID)
			if err != nil {
				fault = app.NewFaultf(errcode.DeleteRecordFail, "Delete Su fail, as[%s]", err.Error())
				return fault.Result()
			}
		}
	}

	return app.NewSuccessResult("Done")
}

func ImportShardingConfigure(request app.IRequest, op *app.Operator, planInfo *release.PlanInfo, argsMaps *map[string]interface{}) *app.Result {
	var fault *app.Fault
	mgr := release2.NewPlanMgr(&planInfo.Plan, request.GetCtx())
	var err error
	if mgr.Entity.Status == release.PlanStatusPreparing {
		mgr.Entity.Status = release.PlanStatusExecuting
		err = mgr.Update("plan_status")
		if err != nil {
			fault = app.NewFaultf(errcode.UpdateRecordFail, "update env configure plan fail,as[%s]", err.Error())
			return fault.Result()
		}
	}
	defer func() {
		if fault != nil {
			mgr.Entity.Status = release.PlanStatusFailed
			mgr.Entity.Description = fault.Error()
			err = mgr.Update("plan_status", "description")
			if err != nil {
				log.Errorf(request.GetCtx(), "update plan fail,as[%s]", err.Error())
			}
		}
	}()
	args := stack.ShardingArgs{}
	err = mapstructure.WeakDecode((*argsMaps)[schedule.JobParamKeyArgs], &args)
	if err != nil {
		fault = app.NewFaultf(errcode.InvalidParameter, "Deploy Service Not Found, as[%s]", err.Error())
		return fault.Result()
	}
	sd := args.Result

	// prepare data
	stMap := make(map[string]workspace.RelSuTypeService, 0)
	for _, st := range args.SuTypes {
		stMap[fmt.Sprint(st.ArtifactBusinessId)] = st
	}
	sdMap := make(map[string]gls2.GlsShardingData, 0)
	sds, err := gls.GetShardingDataList(request, &gls2.GlsShardingDataFilter{
		EnvID: planInfo.EnvironmentID,
	})
	if err != nil {
		fault = app.NewFaultf(errcode.QueryRecordFail, "Get Sharding Data fail, as[%s]", err.Error())
		return fault.Result()
	}
	for _, sd := range sds {
		sdMap[fmt.Sprintf("%s.%s.%s.%s", sd.SuTypeCode, sd.PrimaryCode, sd.SecondaryCode, sd.Key)] = sd
	}
	sdDao, err := gls3.NewGlsShardingDataDao(request)
	if err != nil {
		fault = app.NewFaultf(errcode.InitDaoFail, "Get Sharding Data Dao fail, as[%s]", err.Error())
		return fault.Result()
	}
	psMap := make(map[string]workspace.WorkspaceShardingDataSpec, 0)
	for _, p := range args.Shardings {
		sMap := make(map[string]workspace.WorkspaceShardingAttrSpec, 0)
		for _, s := range p.Seconds {
			sMap[s.ID] = s
		}
		p.Seconds = sMap
		psMap[fmt.Sprintf("%s.%s", p.ArtifactBusinessType, p.ArtifactBusinessId)] = p
	}

	key := fmt.Sprintf("%s.%s.%s.%s", sd.SuTypeId, sd.PrimaryId, sd.SecondaryId, sd.Key)
	now := time.Now()
	switch sd.Result {
	case constants.ComparisonResultAdd, constants.ComparisonResultUpd:
		st, ok := stMap[sd.SuTypeId]
		if !ok {
			fault = app.NewFaultf(errcode.QueryRecordFail, "Su Type[%s] Not Found", sd.SuTypeId)
			return fault.Result()
		}
		p, ok := psMap[fmt.Sprintf("%s.%s", sd.SuTypeId, sd.PrimaryId)]
		if !ok {
			fault = app.NewFaultf(errcode.QueryRecordFail, "Primary Info[%s.%s] Not Found", sd.SuTypeId, sd.PrimaryId)
			return fault.Result()
		}
		s, ok := p.Seconds[sd.SecondaryId]
		if !ok {
			fault = app.NewFaultf(errcode.QueryRecordFail, "Secondary Info[%s.%s.%s] Not Found", sd.SuTypeId, sd.PrimaryId, sd.SecondaryId)
			return fault.Result()
		}
		gd, ok := sdMap[key]
		if ok {
			gd.Value = utils.ValidValue(sd.CurrentValue)
			_, err = sdDao.UpdateGlsShardingData(gd.ID, &gd, "value")
		} else {
			gd = gls2.GlsShardingData{
				EnvID:         planInfo.EnvironmentID,
				SuTypeID:      uint64(st.Id),
				SuTypeCode:    utils.ValidValue(st.ArtifactBusinessId),
				PrimaryID:     p.ID,
				PrimaryCode:   utils.ValidValue(p.ArtifactBusinessId),
				PrimaryUuid:   utils.ValidValue(p.UUID),
				SecondaryCode: utils.ValidValue(s.ID),
				SecondaryUuid: utils.ValidValue(s.Uuid),
				Key:           utils.ValidValue(sd.Key),
				Value:         utils.ValidValue(sd.CurrentValue),
				TenantID:      op.TenantID,
				CreatedAt:     now,
				UpdatedAt:     now,
			}
			_, err = sdDao.CreateGlsShardingData(&gd)
		}
		if err != nil {
			fault = app.NewFaultf(errcode.UpdateRecordFail, "Operate ShardingData fail, as[%s]", err.Error())
			return fault.Result()
		}
	case constants.ComparisonResultDel:
		p, ok := psMap[fmt.Sprintf("%s.%s", sd.SuTypeId, sd.PrimaryId)]
		if ok {
			_, err = sdDao.DeleteById(p.ID)
			if err != nil {
				fault = app.NewFaultf(errcode.UpdateRecordFail, "Delete ShardingData fail, as[%s]", err.Error())
				return fault.Result()
			}
		}
	}

	return app.NewSuccessResult("Done")
}

func ImportServiceConfigure(request app.IRequest, op *app.Operator, planInfo *release.PlanInfo, argsMaps *map[string]interface{}) *app.Result {
	var fault *app.Fault
	mgr := release2.NewPlanMgr(&planInfo.Plan, request.GetCtx())
	var err error
	if mgr.Entity.Status == release.PlanStatusPreparing {
		mgr.Entity.Status = release.PlanStatusExecuting
		err = mgr.Update("plan_status")
		if err != nil {
			fault = app.NewFaultf(errcode.UpdateRecordFail, "update env configure plan fail,as[%s]", err.Error())
			return fault.Result()
		}
	}

	defer func() {
		if fault != nil {
			mgr.Entity.Status = release.PlanStatusFailed
			mgr.Entity.Description = fault.Error()
			err = mgr.Update("plan_status", "description")
			if err != nil {
				log.Errorf(request.GetCtx(), "update plan fail,as[%s]", err.Error())
			}
		}
	}()

	config := &stack.EnvironmentConfigure{}
	err = json.Unmarshal([]byte(planInfo.Parameter), config)
	if err != nil {
		fault = app.NewFaultf(errcode.JsonUnmarshalFail, "Unmarshal import result fail,as[%s]", err.Error())
		return fault.Result()
	}
	pos := fmt.Sprint((*argsMaps)[schedule.JobParamKeyArgs])
	if pos == "" {
		fault = app.NewFaultf(errcode.JsonUnmarshalFail, "get empty result position")
		return fault.Result()
	}
	p, err := strconv.Atoi(pos)
	if err != nil {
		fault = app.NewFaultf(errcode.InvalidParameter, "Get JobParamKeyArgs fail, as[%s]", err.Error())
		return fault.Result()
	}

	sv := config.Result.ServiceList[p]
	if sv.ServiceId == "" {
		fault = app.NewFaultf(errcode.InvalidParameter, "Get Empty ServiceList")
		return fault.Result()
	}

	patch

	session := request.GetSession()
	if session == nil {
		session, err = database.NewSession()
		if err != nil {
			fault = app.NewFaultf(errcode.InitDaoFail, "Init session fail,as[%s]", err.Error())
			return fault.Result()
		}
		request.SetSession(session)
	}

	// update runtime configure
	rtDao, err := dao.NewRuntimeConfigDao(session, request.GetCtx())
	if err != nil {
		fault = app.NewFaultf(errcode.InitDaoFail, "Init Runtime Config Dao fail,as[%s]", err.Error())
		return fault.Result()
	}
	// get all the runtime configs
	sts, err := stack.GetRuntimeConfigInfoList(&stack2.RuntimeConfigFilter{
		BaseFilter: information.BaseFilter{
			Ctx: request.GetCtx(),
		},
		EnvironmentID: planInfo.EnvironmentID,
		TenantID:      op.TenantID,
		Entity:        utils.ValidValue(sv.ServiceId),
		EntityType:    stack2.EntityTypeService,
		Category:      utils.ValidValue(sv.ServiceType),
	})
	if err != nil {
		fault = app.NewFaultf(errcode.QueryRecordFail, "query runtime config dao fail,as[%s]", err.Error())
		return fault.Result()
	}
	stMap := make(map[string]stack2.RuntimeConfigInfo, 0)
	for _, st := range sts {
		stMap[st.Path] = st
	}
	now := time.Now()
	for _, st := range sv.ConfigsResult {
		rk, ok := constants.ServiceRuntimeConfigKeyMap[st.Key]
		if ok {
			var rt stack2.RuntimeConfigInfo
			rt, ok = stMap[rk]
			if ok {
				rt.EntityName = sv.CurrentName
				rt.Value = fmt.Sprint(st.CurrentValue)
				switch st.Result {
				case constants.ComparisonResultAdd, constants.ComparisonResultUpd:
					_, err = rtDao.UpdateRuntimeConfig(&rt.RuntimeConfig, "entity_name", "value")
				case constants.ComparisonResultDel:
					_, err = rtDao.DeleteRuntimeConfig(rt.RuntimeConfig.ID)
				}
				if err != nil {
					fault = app.NewFaultf(errcode.UpdateFileFail, "update runtime config dao fail,as[%s]", err.Error())
					return fault.Result()
				}
			} else {
				if st.Result == constants.ComparisonResultAdd || st.Result == constants.ComparisonResultUpd {
					// create
					_, err = rtDao.ModifyRuntimeConfig(&stack2.RuntimeConfig{
						Entity:        utils.ValidValue(sv.ServiceId),
						EntityType:    stack2.EntityTypeService,
						Category:      utils.ValidValue(sv.ServiceType),
						EntityName:    utils.ValidValue(sv.CurrentName),
						EnvironmentID: planInfo.EnvironmentID,
						Path:          utils.ValidValue(rk),
						Value:         fmt.Sprint(st.CurrentValue),
						Label:         st.Key,
						TenantID:      op.TenantID,
						CreatorID:     op.UserID,
						Creator:       op.Name,
						UpdaterID:     op.UserID,
						Updater:       op.Name,
						CreatedAt:     now,
						UpdatedAt:     now,
					})
					if err != nil {
						fault = app.NewFaultf(errcode.InsertRecordFail, "create runtime config dao fail,as[%s]", err.Error())
						return fault.Result()
					}
				}
			}
		}
	}

	// // get patch
	// pts, err := configure.GetPatchList(&configure2.PatchFilter{
	// 	PatchType:     configure2.PatchTypePersistent,
	// 	PlanID:        planInfo.ID,
	// 	ServiceCode:   sv.ServiceId,
	// 	ServiceType:   sv.ServiceType,
	// 	EnvironmentID: planInfo.EnvironmentID,
	// 	TenantID:      op.TenantID,
	// })
	// if err != nil {
	// 	return app.NewFailResultf(errcode.QueryRecordFail, "query runtime patch configure fail,as[%s]", err.Error())
	// }
	// ptDao, err := configure3.NewPatchDao(session)
	// if err != nil {
	// 	return app.NewFailResultf(errcode.InitDaoFail, "get runtime patch configure dao fail,as[%s]", err.Error())
	// }
	// for _, pt := range pts {
	// 	// update planId = 0, status = configure.PatchStatusWaiting
	// 	pt.PlanID = 0
	// 	pt.Status = configure2.PatchStatusAccept
	// 	_, err = ptDao.UpdatePatch(&pt, "plan_id", "status")
	// 	if err != nil {
	// 		return app.NewFailResultf(errcode.UpdateFileFail, "update runtime patch configure fail,as[%s]", err.Error())
	// 	}
	// }

	return app.NewSuccessResult("Done")
}

func ImportCompleteConfigure(request app.IRequest, op *app.Operator, planInfo *release.PlanInfo, argsMaps *map[string]interface{}) *app.Result {
	mgr, err := release2.NewPlanMgrByID(planInfo.ID, request.GetCtx())
	if err != nil {
		return app.NewFailResultf(errcode.QueryRecordFail, "query env configure plan fail,as[%s]", err.Error())
	}
	mgr.Entity.Status = release.PlanStatusFinished
	err = mgr.Update("plan_status")
	if err != nil {
		return app.NewFailResultf(errcode.InsertRecordFail, "update env configure plan fail,as[%s]", err.Error())
	}

	return app.NewSuccessResult("Done")
}

// func GetSuTypeList(request app.IRequest) ([]workspace.RelSuTypeService, error) {
// 	var cli = workspace.NewSpecClient(request)
// 	var sts = make([]workspace.RelSuTypeService, 0)
// 	var filter = map[string]interface{}{
// 		"status": workspace.StatusActive,
// 	}
// 	fault := cli.QuerySpecDataList(workspace.ListArtifactTopic, &sts, "suType", filter, "data")
// 	if fault != nil {
// 		return nil, app.NewFailResultf(errcode.QueryRecordFail, "QuerySpecDataList fail,as[%s]", fault.Error())
// 	}

// 	return sts, nil
// }
// func GetPrimaryList(request app.IRequest) ([]workspace.WorkspaceShardingDataSpec, error) {
// 	var cli = workspace.NewSpecClient(request)
// 	var shardings = make([]workspace.WorkspaceShardingDataSpec, 0)
// 	var filter = map[string]interface{}{
// 		"status": workspace.StatusActive,
// 	}
// 	fault := cli.QuerySpecDataList(workspace.ListArtifactTopic, &shardings, "shardingData", filter, "data")
// 	if fault != nil {
// 		return nil, app.NewFailResultf(errcode.QueryRecordFail, "QuerySpecDataList fail,as[%s]", fault.Error())
// 	}

// 	for pos, sharding := range shardings {
// 		if sharding.Attr != "null" {
// 			sharding.Seconds = make(map[string]workspace.WorkspaceShardingAttrSpec, 0)
// 			err := json.Unmarshal([]byte(sharding.Attr), &shardings[pos].Seconds)
// 			if err != nil {
// 				return nil, app.NewFailResultf(errcode.JsonUnmarshalFail, "Unmarshal fail,as[%s]", err.Error())
// 			}
// 		}
// 	}

// 	return shardings, nil
// }
